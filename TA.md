## **技术架构文档: Prompt 管理 Chrome 插件**

**1. 系统概述**

本项目旨在开发一款 Chrome 浏览器插件，提供 Prompt 管理功能。核心架构将遵循 Manifest V3 规范，采用模块化设计，确保可维护性和未来扩展性。主要技术栈包括 React (用于面板 UI)、Webpack (用于项目构建) 和 `chrome.storage.local` (用于数据存储)。

**2. 整体架构图 (概念性)**

```
+-----------------------------------+       +---------------------------------+
|         用户浏览器界面             |       |        Chrome 浏览器内核         |
+-----------------------------------+       +---------------------------------+
           ^      |                                 ^      |
           |      | (用户交互, DOM 操作)              |      | (API 调用)
           |      v                                 |      v
+-----------------------------------+       +---------------------------------+
|      Content Script (content.js)  |------>| Background Script (background.js)|
|  - 注入面板 iframe                |       | (Service Worker)                |
|  - 监听 "#" 快捷输入             |<------|  - 监听快捷键 (Alt+S/W)         |
|  - 与 iframe 通信 (postMessage)   |       |  - 核心数据 CRUD (via chrome.storage)|
|  - 操作页面输入框 (Prompt 上屏)   |       |  - 跨组件消息路由                |
+-----------------------------------+       +---------------------------------+
           ^      |                                 ^      |
           |      | (iframe 加载, 消息传递)           |      | (数据读写)
           |      v                                 |      v
+-----------------------------------+       +---------------------------------+
|      面板 UI (panel.html in iframe)|       |    Chrome Storage (local)       |
|  - React 应用                     |<----->|  - Prompts 数据 (JSON)          |
|  - 搜索框、Prompt 列表、文件夹    |       |  - 文件夹数据 (JSON)           |
|  - 添加/编辑界面                 |       |  - 用户配置 (未来)              |
|  - 与 Content Script 通信         |       +---------------------------------+
+-----------------------------------+
```

**3. 模块详解**

*   **3.1. `manifest.json` (插件清单)**
    *   **`manifest_version`**: 3
    *   **`name`**: "优雅 Prompt 管理器" (或最终定名)
    *   **`version`**: "0.1.0" (初始版本)
    *   **`description`**: "一款优雅高效的 Prompt 管理 Chrome 插件。"
    *   **`permissions`**:
        *   `storage`: 用于访问 `chrome.storage.local`。
        *   `activeTab`: 当用户通过快捷键或边缘触发面板时，需要操作当前激活的标签页。
        *   `scripting`: 用于通过 `chrome.scripting.executeScript` 和 `chrome.scripting.insertCSS` 动态注入 Content Script 和样式（如果需要动态注入）。
        *   `sidePanel` (可选，如果未来考虑官方 Side Panel API 作为备选方案)。
    *   **`background`**:
        *   `service_worker`: "background.js"
    *   **`content_scripts`**:
        *   `matches`: ["<all_urls>"] (或更精确的匹配，例如 `http://*/*`, `https://*/*`)
        *   `js`: ["content.js"]
        *   `css`: (可选，用于注入一些基础的 iframe 容器样式)
    *   **`commands`**:
        *   `_execute_browser_action` (或自定义命令，如 `toggle-panel`):
            *   `suggested_key`: { "default": "Alt+S", "mac": "Alt+S" } (可配置多个快捷键)
    *   **`web_accessible_resources`**:
        *   需要列出 `panel.html` 和其他面板 UI 需要的静态资源 (如图片、字体)，以便 `iframe` 可以访问它们。
        *   `resources`: ["panel.html", "panel.css", "panel.js", "assets/*"] (示例路径)
    *   **`action`**: (定义浏览器右上角插件图标的行为，即使我们主要通过快捷键和边缘触发，也应该有一个图标)
        *   `default_popup`: (可以是一个简单的说明页面，或者直接触发面板显示)
        *   `default_icon`: "icons/icon16.png", "icons/icon48.png", "icons/icon128.png"

*   **3.2. `background.js` (Service Worker)**
    *   **职责：**
        *   监听 `chrome.commands.onCommand` 事件，处理快捷键唤醒面板的逻辑。
        *   管理与 `chrome.storage.local` 的数据交互接口（CRUD 操作：创建、读取、更新、删除 Prompts 和文件夹）。
        *   作为消息中转站，处理来自 Content Script 或面板 UI 的消息，并进行相应操作或转发。例如，Content Script 请求显示面板，Background Script 通知对应的 Content Script 执行操作。
        *   处理导入导出逻辑。
    *   **技术：** 原生 JavaScript。

*   **3.3. `content.js` (Content Script)**
    *   **职责：**
        *   在符合条件的页面加载时注入。
        *   接收来自 Background Script 的指令，动态创建/销毁/显示/隐藏作为面板的 `iframe`。
        *   监听鼠标移动到浏览器右侧边缘的事件，触发面板显示。
        *   监听用户在页面输入框中输入 "#" 的行为，触发自动补全逻辑。
        *   与 `iframe` (面板 UI) 进行双向通信 (使用 `window.postMessage`)：
            *   向 `iframe` 发送数据 (例如，当前页面的 URL 或选中的文本，如果未来需要)。
            *   接收来自 `iframe` 的指令 (例如，"将此 Prompt 上屏")，并操作当前页面的 DOM (找到活动输入框并填入内容)。
        *   确保 `iframe` 的样式和行为不会被页面本身干扰（例如，设置合适的 `z-index`）。
    *   **技术：** 原生 JavaScript。

*   **3.4. `panel.html`, `panel.js`, `panel.css` (面板 UI)**
    *   **`panel.html`**:
        *   作为 React 应用的挂载点 (例如，一个 `<div id="root"></div>`)。
        *   引入打包后的 `panel.js` 和 `panel.css`。
    *   **`panel.js` (由 Webpack 打包 React 应用生成):**
        *   **UI 框架：** React。
        *   **核心组件：**
            *   `App`: 根组件，管理整体布局和状态。
            *   `SearchBar`: 搜索框组件。
            *   `PromptList`: 展示 Prompt 列表的组件，包含 `PromptItem` 子组件。
            *   `PromptItem`: 单个 Prompt 的展示组件，处理选中、预览等。
            *   `FolderList`: 展示文件夹列表的组件 (如果文件夹也作为一个可交互列表)。
            *   `EditorForm`: 添加/编辑 Prompt 的表单组件。
        *   **状态管理：**
            *   初期可使用 React 内置的 `useState`, `useContext`, `useReducer`。
            *   如果状态逻辑变得非常复杂，未来可考虑引入轻量级状态管理库如 Zustand 或 Jotai (Redux 对于此规模可能过重)。
        *   **功能逻辑：**
            *   从 `chrome.storage.local` (通过与 Background Script 通信，或Content Script 转发) 获取 Prompts 和文件夹数据。
            *   实现实时搜索过滤逻辑 (原生 JS 数组操作)。
            *   处理用户交互：选中、回车上屏、点击编辑/删除按钮。
            *   与 Content Script 通信 (使用 `window.parent.postMessage`)，请求上屏 Prompt 或其他需要操作主页面的行为。
            *   实现拖拽排序功能 (使用如 `dnd-kit` 的 React 拖拽库)。
    *   **`panel.css`**:
        *   面板的样式文件。可使用 CSS Modules 或 Styled Components (如果团队熟悉) 来避免样式冲突，或纯 CSS/SCSS。
    *   **技术：** React, JavaScript (ES6+), HTML5, CSS3/SCSS。

*   **3.5. 数据存储 (`chrome.storage.local`)**
    *   **数据结构 (示例):**
        ```json
        {
          "prompts": [
            {
              "id": "uuid-1", // 唯一ID，可用 uuid 生成
              "title": "邮件开头",
              "description": "快速生成专业的邮件问候语",
              "content": "尊敬的{{姓名}}先生/女士：\n您好！",
              "folderId": "folder-uuid-1", // 指向文件夹ID，若无则为空或特定值
              "createdAt": "timestamp",
              "updatedAt": "timestamp",
              "usageCount": 0, // 可选，用于智能排序
              "lastUsedAt": "timestamp" // 可选
            }
            // ...更多 prompts
          ],
          "folders": [
            {
              "id": "folder-uuid-1",
              "name": "工作邮件",
              "createdAt": "timestamp",
              "order": 0 // 用于排序
            }
            // ...更多 folders
          ]
        }
        ```
    *   所有数据以 JSON 格式存储。Background Script 将提供统一的 API 供其他模块访问和修改这些数据。

*   **3.6. 构建系统 (Webpack)**
    *   **入口点：**
        *   `panel.js` (React 应用的入口)
        *   `background.js`
        *   `content.js`
    *   **输出：** 将上述 JS 文件打包到 `dist` 目录，并处理依赖、转译 (Babel for ES6+ and React JSX)、CSS 提取/注入等。
    *   **配置 (`webpack.config.js`):**
        *   `mode`: 'development' 或 'production'。
        *   `entry`: 定义入口文件。
        *   `output`: 定义输出路径和文件名。
        *   `module.rules`: 配置 loaders (e.g., `babel-loader` for JS/JSX, `css-loader`, `style-loader`/`MiniCssExtractPlugin` for CSS)。
        *   `plugins`: (e.g., `HtmlWebpackPlugin` for `panel.html`, `CopyWebpackPlugin` for static assets and `manifest.json`)。
        *   `devtool`: 'source-map' for debugging.

**4. 关键技术点与决策**

*   **通信机制：**
    *   **Content Script <-> Background Script:** 使用 `chrome.runtime.sendMessage` 和 `chrome.runtime.onMessage`。
    *   **Content Script <-> Panel UI (iframe):** 使用 `window.postMessage` 和 `window.addEventListener('message', ...)`。这是跨源通信的标准方式。Content Script 向 iframe 发消息用 `iframe.contentWindow.postMessage`，iframe 向父页面 (Content Script) 发消息用 `window.parent.postMessage`。
*   **ID 生成：** 对于 Prompts 和 Folders，建议使用 UUID (Universally Unique Identifier) 来确保 ID 的唯一性。可以使用 `uuid` 这样的 npm 包。
*   **错误处理与日志：** 在关键逻辑部分添加适当的错误捕获 (`try...catch`) 和日志输出 (`console.log`, `console.error`)，便于调试。
*   **安全性：**
    *   严格遵守 Manifest V3 的内容安全策略 (CSP)。
    *   对来自 `iframe` 的消息进行来源验证 (检查 `event.origin`)，确保只处理来自自己插件 `iframe` 的消息。
    *   对用户输入进行适当的处理，避免潜在的注入风险（虽然主要是在插件内部使用，但养成好习惯）。

**5. 有必要添加的内容 (我认为)**

*   **版本控制：** 使用 Git 进行版本控制。
*   **代码规范与格式化：** 使用 ESLint 和 Prettier 来统一代码风格，提高代码质量和可读性。AI 生成的代码也应该通过这些工具进行格式化。
*   **简单的单元测试/集成测试框架：** 考虑为核心逻辑（例如数据处理、搜索算法）编写一些简单的测试用例，可以使用 Jest 等框架。这对于长期维护和确保 AI 生成代码的正确性有益。
*   **清晰的目录结构：**
    ```
    prompt-manager-extension/
    ├── public/                     # 静态资源，会被复制到 dist
    │   ├── icons/
    │   │   ├── icon16.png
    │   │   └── ...
    │   ├── panel.html
    │   └── manifest.json
    ├── src/
    │   ├── background/
    │   │   └── index.js            # Background script
    │   ├── content/
    │   │   └── index.js            # Content script
    │   ├── panel/                  # React UI application
    │   │   ├── components/         # React components
    │   │   ├── hooks/              # Custom React hooks
    │   │   ├── store/              # State management (if needed)
    │   │   ├── App.js
    │   │   ├── index.js            # Entry point for React app
    │   │   └── index.css           # Global styles for panel
    │   ├── common/                 # 通用工具函数、常量等
    │   │   └── utils.js
    ├── scripts/                    # 构建脚本、辅助脚本
    ├── webpack.config.js
    ├── package.json
    ├── .eslintrc.js
    └── .prettierrc.js
    ```

